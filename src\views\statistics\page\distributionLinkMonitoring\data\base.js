export const base = [
  {
    isCustomGroup: true,
    customColumnsGroupName: '分组维度',
    customColumns: [
      {
        title: '分发来源',
        key: 'sourceStr',
        groupKey: 'source',
        fixed: 'left'
      },
      {
        title: '操作系统',
        key: 'osStr',
        groupKey: 'os',
        fixed: 'left'
      },
      {
        title: '流量类型',
        key: 'trafficType',
        groupKey: 'trafficType',
        fixed: 'left'
      },
      {
        key: 'childProduceTypeStr',
        title: '分发产品',
        groupKey: 'childProduceType',
        fixed: 'left'
      },
      {
        key: 'advertiserStr',
        title: '分发方',
        groupKey: 'advertiserCode',
        fixed: 'left'
      }
    ]
  }
]

export const base2 = [
  {
    customColumnsGroupName: '登录侧',
    customColumns: [
      {
        key: 'loginCount',
        title: '注册/登录数',
        tips: '站内&站外注册/登录用户数，包含静默登录，用户去重',
        sortable: true
      }
    ]
  },
  {
    customColumnsGroupName: '留资侧',
    customColumns: [
      {
        key: 'leadCount',
        title: '信息提交数',
        tips: '信息提交用户数，用户去重',
        sortable: true
      },
      {
        key: 'userInfoPageFinishUv',
        title: '留资完成数',
        tips: '留资完成用户数，用户去重',
        sortable: true
      }
    ]
  },
  {
    customColumnsGroupName: '分发侧',
    customColumns: [
      // {
      //   key: 'triageSuccessPageViewCount',
      //   title: '分发成功页访问数',
      //   tips: '站内&站外分发成功页访问用户数，用户手机号去重',
      //   sortable: true
      // },
      {
        key: 'ownerRequestCount',
        title: '撞库请求数',
        tips: '分发撞库请求数，用户手机号去重，仅分发引擎存在该节点',
        sortable: true
      },
      {
        key: 'ownerRequestSuccessCount',
        title: '撞库成功数',
        tips: '分发撞库请求成功数，用户手机号去重，仅分发引擎存在该节点',
        sortable: true
      },
      {
        key: 'distributeRequests',
        title: '推送请求数',
        tips: '分发推送请求数，用户手机号去重',
        sortable: true
      },
      {
        key: 'distributeSuccessCount',
        title: '推送成功数',
        tips: '分发推送请求成功数，用户手机号去重',
        sortable: true
      },
      {
        key: 'clickApplicationCount',
        title: '点击申请数',
        tips: '站内&站外分发成功页点击申请用户数，用户手机号去重',
        sortable: true
      },
      {
        key: 'applySuccessCount',
        title: '申请成功数',
        tips: '站内&站外申请成功用户数，用户手机号去重',
        sortable: true
      },
      {
        key: 'estimatedRevenue',
        title: '预估收益',
        tips: '站内&站外分发成功预估收益，存在多条分发成功记录时，取最大值，用户手机号去重',
        sortable: true
      },
      {
        key: 'actualRevenue',
        title: '实际收益',
        tips: '站内&站外申请成功实际收益，用户手机号去重',
        sortable: true
      },
      {
        key: 'preAssetValuationRequestRate',
        title: '预估分发均价',
        tips: '站内&站外分发成功的均价，计算公式：预估收益/推送成功数',
        sortable: true
      },
      {
        key: 'assetValuationRequestRate',
        title: '实际分发均价',
        tips: '站内&站外申请成功的均价，计算公式：实际收益/申请成功数',
        sortable: true
      },
      {
        key: 'improveApplySuccessRate',
        title: '留资-申请成功率',
        tips: '计算公式：申请成功数/留资数',
        sortable: true
      },
      {
        key: 'acquisitionRate',
        title: '收单率',
        tips: '计算公式：申请成功数/推送成功数*100%',
        sortable: true
      }
      // {
      //   key: 'triageSuccessfulApplicationRate',
      //   title: '分发成功-申请成功率',
      //   tips: '申请成功数/推送成功数*100%',
      //   sortable: true
      // },
      // {
      //   key: 'improveTriageRate',
      //   title: '留资-分发请求率',
      //   tips: '计算公式：推送请求数/留资数',
      //   sortable: true
      // },
      // {
      //   key: 'improveTriageSuccessRate',
      //   title: '分发请求-分发成功率',
      //   tips: '计算公式：推送成功数/推送请求数',
      //   sortable: true
      // },
      // {
      //   key: 'triageSuccessPageViewRate',
      //   title: '分发成功-分发成功页访问率',
      //   tips: '计算公式：分发成功页访问数/推送成功数',
      //   sortable: true
      // },
      // {
      //   key: 'triageSuccessClickRate',
      //   title: '分发成功页访问-点击申请率',
      //   tips: '计算公式：点击申请数/分发成功页访问数',
      //   sortable: true
      // },
      // {
      //   key: 'clickApplySuccessRate',
      //   title: '点击申请-申请成功率',
      //   tips: '计算公式：申请成功数/点击申请数',
      //   sortable: true
      // }
    ]
  }
]
