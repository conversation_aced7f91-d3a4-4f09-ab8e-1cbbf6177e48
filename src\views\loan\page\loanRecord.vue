<template>
  <div>
    <page :request="request" :list="list" table-title="贷款记录列表">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain type="warning" size="small" icon="el-icon-download" @click="handExport">导出</el-button>
      </div>
    </page>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import {tableItemType, formItemType} from '@/config/sysConfig'
import {getLoanRecordList, exportLoanRecord} from '@/api/loan'
import moment from 'moment'
const applicationScreen = [
  {
    label: '联登媒体延迟进件',
    value: 1,
    tips: '联登媒体通过延迟消息提交进件'
  },
  {
    label: '留资进件',
    value: 2,
    tips: '用户通过手动留资进件'
  },
  {
    label: '按揭-未结清分发进件',
    value: 3,
    tips: '用户通过手动留资车辆状态选择按揭-未结清分发进件'
  },
  {
    label: '撞库失败分发进件',
    value: 4,
    tips: '用户通过手动留资撞库失败分发进件'
  },
  {
    label: '非联登媒体延迟进件',
    value: 5,
    tips: '非联登媒体延迟进件'
  },
  {
    label: '全流程API进件CPA结算',
    value: 6,
    tips: '全流程API进件CPA结算'
  },
  {
    label: '全流程API进件CPS结算',
    value: 7,
    tips: '全流程API进件CPS结算'
  }
]
// 进件类型
const applicationTypeList = [
  {
    label: '风控进件',
    value: '1'
  },
  {
    label: '线索进件',
    value: '2'
  },
  {
    label: '分发进件',
    value: '3'
  }
]
const addWechatList = [
  {
    label: '未加微',
    value: 0
  },
  {
    label: '已加微',
    value: 1
  }
]
const followUpList = [
  {
    label: '自有CRM',
    value: 0
  },
  {
    label: '易顺CRM',
    value: 1
  }
]
export default {
  components: {
    page
  },
  data() {
    return {
      orderNodeList: [
        {id: '-000', name: '创建'},
        {id: '003', name: '预审生成'},
        {id: '010', name: '预审审批'},
        {id: '110', name: '电核'},
        {id: '210', name: '授信'},
        {id: '310', name: '放款'}
      ],
      nodeStatusList: [
        {id: 1, name: '通过'},
        {id: 3, name: '拒绝'}
      ],
      listQuery: {
        startDate: moment().subtract(5, 'days').format('YYYYMMDD'),
        endDate: moment().format('YYYYMMDD')
      },
      request: {
        getListUrl: async data => {
          this.listQuery = {...this.listQuery, ...data}
          const res = await getLoanRecordList(this.listQuery)
          return {
            data: {
              total: res.data.total,
              rows: res.data.records
            }
          }
        }
      }
    }
  },
  computed: {
    list() {
      return [
        {
          title: '日期',
          titleHidden: true,
          key: 'date',
          type: formItemType.rangeDatePicker,
          childKey: ['startDate', 'endDate'],
          options: {
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyyMMdd'
          },
          val: [this.listQuery.startDate, this.listQuery.endDate],
          search: true,
          pickerDay: 90,
          formHidden: true
        },
        {
          title: '贷款单号',
          key: 'outOrderNo',
          search: true,
          titleHidden: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '贷款单号'
          }
        },
        {
          title: '进件编号',
          key: 'thirdOutOrderNo',
          search: true,
          titleHidden: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '进件编号'
          }
        },
        {
          title: '用户ID',
          key: 'userId',
          search: true,
          titleHidden: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '用户ID'
          }
        },
        {
          title: '用户手机号',
          key: 'mobileNo',
          search: true,
          titleHidden: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '用户手机号'
          }
        },
        {
          title: '渠道ID',
          key: 'channelCode',
          search: true,
          titleHidden: true,
          tableHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '渠道ID'
          }
        },
        {
          title: '跟进方',
          key: 'followUpOwner',
          search: true,
          type: formItemType.select,
          list: followUpList,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          titleHidden: true,
          options: {
            placeholder: '跟进方'
          }
        },
        {
          title: '当前节点',
          key: 'orderNode',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: this.orderNodeList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '当前状态',
          key: 'nodeStatus',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: this.nodeStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '贷款来源',
          key: 'source', // 1:app 2:h5
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: [
            {
              label: 'app',
              value: 1
            },
            {
              label: 'h5',
              value: 2
            }
          ]
        },
        {
          title: '进件场景',
          key: 'intoScene1',
          searchKey: 'intoScene',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          list: applicationScreen,
          tableHidden: true
        },
        {
          title: '子节点',
          key: 'orderChildNode', // 1:身份认证,2:人车验证,3:风控前筛,4:车辆状态认证
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: [
            {
              label: '身份认证',
              value: 1
            },
            {
              label: '人车验证',
              value: 2
            },
            {
              label: '风控前筛',
              value: 3
            },
            {
              label: '车辆状态认证',
              value: 4
            },
            {
              label: '电销跟进',
              value: 5
            }
          ]
        },
        {
          title: '进件类型',
          key: 'orderType',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: applicationTypeList
        },
        {
          title: '加微状态',
          key: 'addWechat',
          search: true,
          titleHidden: true,
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          tableHidden: true,
          list: addWechatList
        },
        {
          title: '序号',
          key: 'sort',
          render: (h, params) => {
            return h('span', params.data.$index + 1)
          }
        },
        {
          title: '贷款单号',
          key: 'outOrderNo'
        },
        {
          title: '进件编号',
          key: 'thirdOutOrderNo'
        },
        {
          title: '策略ID',
          key: 'strategyId'
        },
        {
          title: '贷款来源',
          key: 'sourceStr'
        },
        {
          title: '进件类型',
          key: 'carLoanTypeStr'
        },
        {
          title: '跟进方',
          key: 'followUpOwnerStr'
        },
        {
          title: '进件场景',
          key: 'intoScene',
          list: applicationScreen,
          width: 150,
          render: (h, params) => {
            const info = applicationScreen.find(item => String(item.value) === String(params.data.row.intoScene)) ?? {}
            return info.value ? (
              <el-tooltip class="item" effect="dark" content={info.tips} placement="top">
                <span>{info.label}</span>
              </el-tooltip>
            ) : (
              <span>-</span>
            )
          }
        },
        {
          title: '用户ID',
          key: 'userId'
        },
        {
          title: '用户手机号',
          key: 'mobileNo',
          width: 90
        },
        {
          title: '渠道ID',
          key: 'channelCode'
        },
        {
          title: '线索类型',
          key: 'clueTypeStr'
        },
        {
          title: '线索状态',
          key: 'clueStatusStr'
        },
        {
          title: '线索跟进状态',
          key: 'leadFollowUpStatusStr'
        },
        {
          title: '当前节点',
          key: 'orderNode',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.orderNodeList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '子节点',
          key: 'childOrderNodeStr'
        },
        {
          title: '当前状态',
          key: 'currentStatus',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: this.nodeStatusList,
          listFormat: {
            label: 'name',
            value: 'id'
          }
        },
        {
          title: '拒绝原因',
          key: 'failMessageDetail'
        },
        {
          title: '担保方',
          key: 'guaranteeCode'
        },
        {
          title: '放款方',
          key: 'capitalCode'
        },
        {
          title: '授信金额',
          key: 'creditLine'
        },
        {
          title: '利率',
          key: 'sfProductRate'
        },
        {
          title: '期数',
          key: 'sfTerm'
        },
        {
          title: '月供',
          key: 'monthlyPayment'
        },
        {
          title: '实际放款金额',
          key: 'sfLoanAmount',
          width: 120
        },
        {
          title: '分润佣金',
          key: 'commissionSharing',
          width: 120
        },
        {
          title: '加微状态',
          key: 'addWechat',
          type: formItemType.select,
          tableView: tableItemType.tableView.text,
          list: addWechatList
        },
        {
          title: '创建时间',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '预审通过时间',
          key: 'approvalTime',
          width: 120,
          render: (h, params) => {
            if (!params.data.row.approvalTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.approvalTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '客服电核通过时间',
          key: 'mobileAuditTime',
          width: 120,
          render: (h, params) => {
            if (!params.data.row.mobileAuditTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.mobileAuditTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '授信通过时间',
          key: 'creditAuditTime',
          width: 120,
          render: (h, params) => {
            if (!params.data.row.creditAuditTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.creditAuditTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '放款时间',
          key: 'fundApprovalTime',
          render: (h, params) => {
            if (!params.data.row.fundApprovalTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.fundApprovalTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        }
      ]
    }
  },
  methods: {
    handExport() {
      // window.location.href = exportLoanRecord(this.listQuery)
      const data = {
        ...this.listQuery
      }
      window.location.href = exportLoanRecord({
        ...data,
        token: this.$store.getters.authorization
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .activeButton {
  .el-button {
    margin-right: 5px;
    padding: 7px 6px;
  }
}
</style>
