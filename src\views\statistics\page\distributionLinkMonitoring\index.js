import { getList, getDetailList, listExport, getChannelCodeList, getChannelCodeDetailList, channelCodeListExport } from '@/api/distributionLinkMonitoring'
import dateList from '@/views/statistics/page/distributionLinkMonitoring/data/date'
import channelList from '@/views/statistics/page/distributionLinkMonitoring/data/channel'

export const config = {
  apiConfig: {
    list: getList,
    detail: getDetailList,
    export: listExport,
    channelList: getChannelCodeList,
    channelDetail: getChannelCodeDetailList,
    channelExport: channelCodeListExport
  },
  localKeys: {
    list: 'distributionLinkMonitoring',
    detail: 'distributionLinkMonitoringDetail'
  },
  channelPath: '/statistics/distributionLinkMonitoring/channel',
  title: '分发链路监控',
  titleTips: '分发节点说明：不同分发方存在不同的分发节点，节点与节点之间为串行，上一个节点成功后执行下一个节点<br>数信：推送-申请<br>分发引擎：撞库-推送-申请',
  dateList,
  channelList,
  multipleSelectKeys: ['apiCodeStr', 'landingPageIdStr', 'cityIdStr'],
  paramKeys: {
    apiCode: 'apiCodeStr',
    landingPage: 'landingPageIdStr',
    groupField: 'groupFieldsStr',
    cityId: 'cityIdStr'
  }
}
