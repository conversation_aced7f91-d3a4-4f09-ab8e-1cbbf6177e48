<template>
  <el-cascader ref="cascaderRef" v-bind="$attrs">
    <template v-for="(value, name) in $slots" #[name]="slotProps">
      <slot :name="name" v-bind="slotProps"></slot>
    </template>
  </el-cascader>
</template>

<script>
export default {
  name: 'ElCascaderFixed',
  inheritAttrs: false,
  mounted() {
    // 获取 cascader 实例
    const cascader = this.$refs.cascaderRef

    // 重写 deleteTag 方法
    cascader.deleteTag = index => {
      const tag = cascader.presentTags[index]
      if (!tag.node) return

      const val = tag.node.getValueByOption()
      cascader.checkedValue = cascader.checkedValue.filter(v => !this.isEqual(v, val))
      cascader.$emit('remove-tag', val)
    }

    const entries = Object.entries(cascader)
    //将 组件的下的方法 同步到该组件的实例上
    for (const [key, value] of entries) {
      if (typeof value === 'function') {
        this[key] = value
      }
    }
  },
  methods: {
    isEqual(a, b) {
      return JSON.stringify(a) === JSON.stringify(b)
    }
  }
}
</script>
