<template>
  <div>
    <page :request="request" :list="list" table-title="媒体管理">
      <div slot="searchContainer" style="display: inline-block">
        <el-button plain icon="el-icon-circle-plus-outline" type="primary" size="small" @click="handleAdd"
          >新增</el-button
        >
      </div>
    </page>
    <Drawer :visible.sync="drawer" title="全流程媒体管理">
      <MediaEdit
        :modelForm="modelForm"
        :mediaList="mediaList"
        :subLoading="subLoading"
        @close="drawer = false"
        @confirm="handleSubmit"
      />
    </Drawer>
  </div>
</template>

<script>
import page from '@/components/restructure/page'
import Drawer from '@/components/Drawer/FormSection2.vue'
import {tableItemType, formItemType} from '@/config/sysConfig'
import MediaEdit from '@/views/newChannel/module/mediaEdit/index.vue'
import {getFullMedia, add_fullMedia, update_fullMedia, getFullMediaDetail} from '@/api/mediaManagement'
import getFullMediaList from '../module/getFullMedia'
import moment from 'moment'
import {SETTLEMENT_METHOD_MAP} from '@/views/newChannel/enum/mediaList'

export default {
  components: {
    page,
    Drawer,
    MediaEdit
  },
  data() {
    return {
      drawer: false,
      listQuery: {},
      statusList: [
        {label: '上线', value: 1},
        {label: '下线', value: 0}
      ],
      request: {
        getListUrl: async data => {
          this.listQuery = {...this.listQuery, ...data}
          const list = await getFullMedia({...this.listQuery})
          const {records, total} = list.data
          const result = {
            data: {
              total: total,
              rows: records
            }
          }
          return result
        }
      },
      modelForm: {},
      mediaList: [],
      subLoading: false
    }
  },

  computed: {
    list() {
      return [
        {
          title: '规则ID',
          key: 'id'
        },
        {
          title: '全流程媒体',
          key: 'apiCode1',
          searchKey: 'apiCode',
          search: true,
          titleHidden: true,
          tableHidden: true,
          type: formItemType.select,
          list: this.mediaList,
          options: {
            placeholder: '媒体'
          },
          listFormat: {
            label: 'platformName',
            value: 'platformCode'
          }
        },
        {
          title: '全流程媒体',
          key: 'apiCode',
          render: (h, params) => {
            let {apiCode} = params?.data?.row ?? {}
            let row = this.mediaList?.find(item => item.platformCode === apiCode) ?? {}
            return <span>{row.platformName || '-'}</span>
          }
        },
        {
          title: '规则名称',
          key: 'ruleName',
          search: true,
          titleHidden: true,
          type: formItemType.input,
          options: {
            placeholder: '规则名称'
          }
        },
        {
          title: '状态',
          key: 'onlineStatus1',
          searchKey: 'onlineStatus',
          search: true,
          titleHidden: true,
          tableHidden: true,
          type: formItemType.select,
          list: this.statusList,
          options: {
            placeholder: '状态'
          }
        },
        {
          title: '结算方式',
          key: 'settlementMethod',
          searchKey: 'settlementMethod',
          search: true,
          titleHidden: true,
          tableHidden: true,
          type: formItemType.select,
          list: Object.keys(SETTLEMENT_METHOD_MAP).map(key => {
            return {
              label: SETTLEMENT_METHOD_MAP[key],
              value: key
            }
          })
        },
        {
          title: '规则备注',
          key: 'remark',
          width: 80
        },
        {
          title: '结算方式',
          key: 'settlementMethod',
          render: (h, params) => {
            let {settlementMethod} = params?.data?.row ?? null
            return <span>{SETTLEMENT_METHOD_MAP[settlementMethod] || '-'}</span>
          }
        },
        // {
        //   title: '撞库规则',
        //   key: 'channelCode1'
        // },
        // {
        //   title: '线索提交规则',
        //   key: 'channelName'
        // },
        {
          title: '上线状态',
          key: 'onlineStatus',
          type: formItemType.select,
          list: this.statusList,
          tableView: tableItemType.tableView.text
        },
        {
          title: '添加人',
          key: 'createByName'
        },
        {
          title: '添加时间',
          key: 'createTime',
          render: (h, params) => {
            if (!params.data.row.createTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.createTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '更新人',
          key: 'updateByName'
        },
        {
          title: '更新时间',
          key: 'updateTime',
          render: (h, params) => {
            if (!params.data.row.updateTime) {
              return h('span', '--')
            }
            return h('span', moment(params.data.row.updateTime).format('YYYY-MM-DD HH:mm:ss'))
          }
        },
        {
          title: '操作',
          type: tableItemType.active,
          headerContainer: false,
          key: 'operation',
          width: 120,
          fixed: 'right',
          activeType: [
            {
              text: '编辑',
              key: 'edit',
              type: tableItemType.activeType.event,
              click: async ($index, item, params) => {
                const res = await getFullMediaDetail({fullMediaId: params.id})
                this.drawer = true
                this.modelForm = {...(res.data || {})}
              }
            }
          ]
        }
      ]
    }
  },
  created() {
    getFullMediaList().then(res => {
      this.mediaList = res
    })
  },
  methods: {
    handleAdd() {
      this.drawer = true
      this.modelForm = {}
    },
    async handleSubmit(modelForm) {
      this.subLoading = true
      const api = modelForm.id ? update_fullMedia : add_fullMedia
      let res = await api(modelForm).finally(() => {
        this.subLoading = false
      })
      if (res.code === 200) {
        this.$message.success('操作成功')
        this.drawer = false
        this.$store.dispatch('tableRefresh', this)
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
