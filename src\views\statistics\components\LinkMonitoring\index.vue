<template>
  <div class="link-monitoring">
    <!-- 筛选条件区块 -->
    <el-card class="filter-container" shadow="never">
      <el-form ref="filterForm" :inline="true" :model="listQuery" class="filter-form" size="small">
        <!-- 日期范围选择 -->
        <el-form-item>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyyMMdd"
            :picker-options="datePickerOptions"
            @change="handleDateChange"
          />
        </el-form-item>

        <!-- 渠道ID -->
        <el-form-item v-if="!config.hideSearchItems || !config.hideSearchItems.includes('channelCode')">
          <el-input
            v-model="listQuery.channelCode"
            placeholder="渠道ID"
            clearable
          />
        </el-form-item>

        <!-- 投放链路 -->
        <el-form-item>
          <el-select
            v-model="listQuery.downloadType"
            placeholder="投放链路"
            clearable
          >
            <el-option
              v-for="item in downloadTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 投放平台 -->
        <el-form-item>
          <el-select
            v-model="listQuery[config.paramKeys.apiCode]"
            placeholder="投放平台"
            clearable
            multiple
            collapse-tags
            filterable
          >
            <el-option
              v-for="item in apiCodeList"
              :key="item.platformCode"
              :label="item.platformName"
              :value="item.platformCode"
            />
          </el-select>
        </el-form-item>

        <!-- 落地页 -->
        <el-form-item>
          <el-select
            v-model="listQuery[config.paramKeys.landingPage]"
            placeholder="落地页"
            clearable
            multiple
            collapse-tags
            filterable
          >
            <el-option
              v-for="item in landingPageList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <!-- 数据维度 -->
        <el-form-item>
          <el-select
            v-model="listQuery.newType"
            placeholder="数据维度"
            clearable
          >
            <el-option
              v-for="item in newTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 自定义筛选条件插槽 -->
        <slot name="filters" />

        <!-- 搜索按钮 -->
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区块 -->
    <el-card class="table-container" shadow="never">
      <div slot="header" class="card-header">
        <span>{{ config.title }}
          <el-tooltip v-if="config.titleTips" placement="top">
            <i class="el-icon-question" />
            <template slot="content">
              <div v-html="config.titleTips" />
            </template>
          </el-tooltip>
        </span>
        <div class="header-operations">
          <el-button
            type="primary"
            size="small"
            plain
            icon="el-icon-s-operation"
            @click="showCustomColumnDialog = true"
          >自定义列设置</el-button>
          <el-button
            type="primary"
            size="small"
            plain
            icon="el-icon-s-grid"
            @click="showCustomGroupDialog = true"
          >自定义分组</el-button>
          <el-button
            type="warning"
            size="small"
            icon="el-icon-download"
            plain
            @click="handExport"
          >导出</el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableDataBySort"
        :height="700"
        border
        row-key="id"
        lazy
        :load="loadChild"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        :row-class-name="tableRowClassName"
        @sort-change="handleSortChange"
      >
        <template v-for="item in displayColumns">
          <el-table-column
            v-if="!item.children"
            :key="item.key"
            :prop="item.key"
            :label="item.title"
            v-bind="item"
            align="center"
            :sortable="item.sortable ? 'custom' : false"
            :formatter="(row, column, cellValue, index) => cellValue === '' || cellValue === null || cellValue === undefined ? '-' : cellValue"
          >
            <template v-if="item.tips" slot="header">
              <span>{{ item.title }}</span>
              <el-tooltip :content="item.tips" placement="top">
                <i class="el-icon-question" style="margin-left: 5px" />
              </el-tooltip>
            </template>
          </el-table-column>
        </template>

        <el-table-column
          v-if="!isChannelPage"
          label="操作"
          align="center"
          :width="config.operateColumnWidth || 100"
          fixed="right"
        >
          <template slot-scope="scope">
            <!-- 默认操作按钮 -->
            <el-button
              v-if="(scope.row.date !== '综合汇总') && (!config.hideSearchItems || !config.hideSearchItems.includes('channelCode') && scope.row.channelCode !== '综合汇总')"
              type="text"
              size="small"
              @click="handleViewDetails(scope.row)">
              渠道详情
            </el-button>

            <!-- 自定义操作按钮插槽，传递行数据和搜索参数 -->
            <slot name="table-operations" :row="scope.row" :search-params="listQuery"></slot>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page.sync="listQuery.pageNumber"
          :page-sizes="[10, 20, 30, 50]"
          :page-size.sync="listQuery.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 自定义列设置弹窗 -->
    <custom-column-dialog
      :visible.sync="showCustomColumnDialog"
      :column-list="tableColumns"
      :default-selected="selectedColumns"
      type="column"
      @confirm="handleCustomColumnConfirm"
    />

    <!-- 自定义分组设置弹窗 -->
    <custom-column-dialog
      :visible.sync="showCustomGroupDialog"
      :column-list="tableColumns"
      :default-selected="selectedGroups"
      type="group"
      @confirm="handleCustomGroupConfirm"
    />
  </div>
</template>

<script>
import {undertakelist} from '@/api/operate'
import {mediaAll} from '@/qjjpApi/NewChannel'
import CustomColumnDialog from '@/components/CustomColumnDialog/index.vue'
import {
  initSelectedColumns,
  processTableData,
  processChildData,
  processDisplayColumns,
  defaultDateRange,
  getGroupKeysString,
  removeLocalQueryParams,
  saveQueryParamsByPageType,
  getQueryParamsByPageType,
  saveQueryDateByPageType,
  getQueryDateByPageType,
  initQueryParams,
  saveColumnsByPageType,
  saveGroupsByPageType,
  removeColumnsByPageType,
  removeGroupsByPageType,
  loadSelectedColumnsAndGroups,
  processMultipleSelect,
  extractNumber,
  removeHiddenSearchItems,
  getDatePickerOptions
} from '@/views/statistics/components/LinkMonitoring'

export default {
  name: 'BaseLinkMonitoring',
  components: {
    CustomColumnDialog
  },
  props: {
    /**
     * 链路监控组件配置对象
     * @property {Object} apiConfig - API 接口配置
     *   @property {Function} list - 获取列表数据的接口
     *   @property {Function} detail - 获取详情数据的接口
     *   @property {Function} export - 导出数据的接口
     *   @property {Function} channelList - 获取渠道列表数据的接口
     *   @property {Function} channelDetail - 获取渠道详情数据的接口
     *   @property {Function} channelExport - 导出渠道数据的接口
     * @property {Object} localKeys - localStorage 存储键名配置
     *   @property {string} list - 列表页面的存储键名
     *   @property {string} detail - 详情页面的存储键名
     * @property {string} channelPath - 渠道页面的路由路径
     * @property {string} title - 页面标题
     * @property {Array} dateList - 日期维度的列配置
     * @property {Array} channelList - 渠道维度的列配置
     * @property {Array} multipleSelectKeys - 多选字段列表，用于处理多选值的转换
     * @property {Array} [numberFields] - 需要转换为数字类型的字段列表（可选）
     * @property {Object} paramKeys - 参数键名映射配置
     *   @property {string} apiCode - API编码的参数键名
     *   @property {string} landingPage - 落地页的参数键名
     *   @property {string} groupField - 分组字段的参数键名
     * @property {Array} [hideSearchItems] - 需要隐藏的搜索项列表（可选）
     * @property {Array} [operateColumnWidth] - 操作栏宽度
     */
    config: {
      type: Object,
      required: true,
      validator: (value) => {
        return value.apiConfig &&
          value.localKeys &&
          value.channelPath &&
          value.title &&
          value.dateList &&
          value.channelList &&
          value.multipleSelectKeys &&
          value.paramKeys
      }
    },
    /**
     * 用于双向绑定的查询参数对象
     */
    modelValue: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'reset'],
  data() {
    const isChannelPage = this.$route.path === this.config.channelPath

    // 定义默认查询对象
    const defaultQuery = {
      startDate: defaultDateRange()[0],
      endDate: defaultDateRange()[1],
      downloadType: '', // 投放链路：1-APK链路，2-H5链路
      pageSize: 10,
      pageNumber: 1
    }

    // 如果渠道ID不是隐藏字段，则添加到默认查询对象中
    if (!this.config.hideSearchItems || !this.config.hideSearchItems.includes('channelCode')) {
      defaultQuery.channelCode = ''
    }

    // 为多选字段设置默认空数组
    this.config.multipleSelectKeys.forEach(key => {
      // 如果是隐藏字段，则不设置默认值
      if (!this.config.hideSearchItems || !this.config.hideSearchItems.includes(key)) {
        defaultQuery[key] = []
      }
    })

    // 基类组件的默认数字类型字段
    const defaultNumberFields = ['downloadType'] // 默认将 downloadType 转换为数字类型
    const allNumberFields = [...defaultNumberFields, ...(this.config.numberFields || [])]

    // 获取合并后的查询参数
    const localParams = getQueryParamsByPageType(this.config.localKeys.list, this.config.localKeys.detail, isChannelPage)

    // 获取单独存储的日期参数
    const dateParams = getQueryDateByPageType(this.config.localKeys.list, this.config.localKeys.detail, isChannelPage)

    // 合并日期参数和其他参数
    const mergedLocalParams = { ...localParams, ...dateParams }

    // 初始化查询参数，合并外部传入的值
    let queryParams = {
      ...initQueryParams(mergedLocalParams, this.$route.query, defaultQuery, this.config.multipleSelectKeys, allNumberFields),
      ...this.modelValue
    }

    // 移除隐藏的搜索项
    queryParams = removeHiddenSearchItems(queryParams, this.config.hideSearchItems)

    // console.log('--------------------: ', queryParams)

    return {
      loading: false,
      total: 0,
      tableData: [],
      tableDataBySort: [],
      dateRange: [queryParams.startDate, queryParams.endDate] || defaultDateRange(),
      showCustomColumnDialog: false,
      showCustomGroupDialog: false,
      selectedColumns: [],
      selectedGroups: [],
      defaultListQuery: defaultQuery,
      listQuery: queryParams,
      downloadTypeList: [
        { label: 'APK链路', value: 1 },
        { label: 'H5链路', value: 2 }
      ],
      landingPageList: [],
      apiCodeList: [],
      newTypeList: [
        { label: '新', value: 1 },
        { label: '老', value: 0 }
      ]
    }
  },
  watch: {
    listQuery: {
      handler(val) {
        this.$emit('update:modelValue', val)
      },
      deep: true
    },
    modelValue: {
      handler(val) {
        if (val) {
          this.listQuery = {
            ...this.listQuery,
            ...val
          }
        }
      },
      deep: true
    }
  },
  computed: {
    isChannelPage() {
      return this.$route.path === this.config.channelPath
    },
    tableColumns() {
      return this.isChannelPage ? this.config.channelList : this.config.dateList
    },
    request() {
      return this.isChannelPage ? this.config.apiConfig.channelList : this.config.apiConfig.list
    },
    detailRequest() {
      return this.isChannelPage ? this.config.apiConfig.channelDetail : this.config.apiConfig.detail
    },
    exportRequest() {
      return this.isChannelPage ? this.config.apiConfig.channelExport : this.config.apiConfig.export
    },
    displayColumns() {
      return processDisplayColumns(this.tableColumns, this.selectedColumns, this.selectedGroups)
    },
    datePickerOptions() {
      return getDatePickerOptions(this.config.dateLimit || {})
    }
  },
  created() {
    this.initData()
    this.initSelectedColumns()
  },
  methods: {
    async initData() {
      this.fetchData()

      // 获取落地页列表
      const landingPageRes = await undertakelist({status: 0, pageSize: 999, pageNumber: 1})
      if (landingPageRes.code === 200 && Array.isArray(landingPageRes.data?.records)) {
        this.landingPageList = landingPageRes.data.records
      }

      // 获取投放平台列表
      const platformRes = await mediaAll()
      if (Array.isArray(platformRes.data)) {
        this.apiCodeList = platformRes.data
      }
    },

    async fetchData() {
      this.loading = true
      try {
        // console.log('############## fetchData: ', this.listQuery)
        const { startDate, endDate, ...otherParams } = this.listQuery

        // 保存查询参数到localStorage（不包含日期）
        saveQueryParamsByPageType(otherParams, this.config.localKeys.list, this.config.localKeys.detail, this.isChannelPage)

        // 单独保存日期参数（当天0点过期）
        // console.log('############## startDate: ', startDate)
        // console.log('############## endDate: ', endDate)
        if (startDate && endDate) {
          saveQueryDateByPageType(
            {
              startDate: String(startDate),
              endDate: String(endDate)
            },
            this.config.localKeys.list,
            this.config.localKeys.detail,
            this.isChannelPage
          )
        }

        // 将选中的分组列传入查询参数
        const groupFieldStr = getGroupKeysString(this.selectedGroups, this.tableColumns)

        // 多选字段转为逗号分隔的字符串
        let processedQuery = processMultipleSelect(this.listQuery, this.config.multipleSelectKeys)

        // console.log('############## listQuery: ', this.listQuery)
        // console.log('^^^^^^^^^^^^^^ processedQuery-before: ', processedQuery)

        // 移除隐藏的搜索项
        processedQuery = removeHiddenSearchItems(processedQuery, this.config.hideSearchItems)
        // console.log('^^^^^^^^^^^^^^ processedQuery-after: ', processedQuery)

        const params = {
          ...processedQuery,
          [this.config.paramKeys.groupField]: groupFieldStr
        }

        const res = await this.request(params)
        if (res.data) {
          const processedData = processTableData(res.data.records) || []
          this.tableData = processedData
          this.tableDataBySort = processedData
          this.total = res.data.total || 0

          this.$nextTick(() => {
            this.$refs.tableRef.doLayout()
          })
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    async loadChild(tree, treeNode, resolve) {
      try {
        const { date, channelCode: channelCodeFromTree } = tree

        // 多选字段转为逗号分隔的字符串
        let processedQuery = processMultipleSelect(this.listQuery, this.config.multipleSelectKeys)

        // 移除隐藏的搜索项
        processedQuery = removeHiddenSearchItems(processedQuery, this.config.hideSearchItems)

        // 将选中的分组列传入查询参数
        const groupFieldStr = getGroupKeysString(this.selectedGroups, this.tableColumns)

        const { startDate, endDate, ...otherParams } = processedQuery

        const params = {
          ...otherParams,
          startDate: date || startDate,
          endDate: date || endDate,
          [this.config.paramKeys.groupField]: groupFieldStr
        }

        // 只有当channelCode不在隐藏搜索项中时，才添加该参数
        if (!this.config.hideSearchItems || !this.config.hideSearchItems.includes('channelCode')) {
          params.channelCode = channelCodeFromTree || processedQuery.channelCode
        }

        const res = await this.detailRequest(params)
        if (res.data && res.data.length) {
          const processedChildData = processChildData(res.data, tree)
          resolve(processedChildData)
        } else {
          resolve([])
        }
      } catch (error) {
        console.error('获取子级数据失败:', error)
        resolve([])
      }
    },

    handleDateChange() {
      if (this.dateRange) {
        this.listQuery.startDate = this.dateRange[0]
        this.listQuery.endDate = this.dateRange[1]
      }
    },

    handleSearch() {
      this.listQuery.pageNumber = 1
      this.fetchData()
    },

    handleSizeChange(val) {
      this.listQuery.pageSize = val
      this.fetchData()
    },

    handleCurrentChange(val) {
      this.listQuery.pageNumber = val
      this.fetchData()
    },

    handleReset() {
      // 调用函数获取最新的日期范围
      this.dateRange = defaultDateRange()

      // 重置为默认查询参数
      const { startDate, endDate, ...otherParams } = this.defaultListQuery
      this.listQuery = {
        ...otherParams,
        startDate: defaultDateRange()[0],
        endDate: defaultDateRange()[1]
      }

      // 从本地存储中移除查询参数
      removeLocalQueryParams(this.isChannelPage ? this.config.localKeys.detail : this.config.localKeys.list)

      // 重置表格列和分组设置
      const { selectedColumns, selectedGroups } = initSelectedColumns(this.tableColumns)
      this.selectedColumns = selectedColumns
      this.selectedGroups = selectedGroups

      // 从本地存储中移除列和分组设置
      removeColumnsByPageType(this.config.localKeys.list, this.config.localKeys.detail, this.isChannelPage)
      removeGroupsByPageType(this.config.localKeys.list, this.config.localKeys.detail, this.isChannelPage)

      // 触发重置事件
      this.$emit('reset')

      // 重新获取数据
      this.handleSearch()

      // 清除URL参数，使用无参数的URL替换当前URL，不触发页面跳转
      const path = this.$route.path
      this.$router.replace({ path })
    },

    initSelectedColumns() {
      const { selectedColumns, selectedGroups } = loadSelectedColumnsAndGroups(
        this.tableColumns,
        this.config.localKeys.list,
        this.config.localKeys.detail,
        this.isChannelPage
      )

      this.selectedColumns = selectedColumns
      this.selectedGroups = selectedGroups
    },

    handleCustomColumnConfirm(selectedKeys) {
      this.selectedColumns = selectedKeys
      saveColumnsByPageType(selectedKeys, this.config.localKeys.list, this.config.localKeys.detail, this.isChannelPage)
      this.$nextTick(() => {
        this.$refs.tableRef.doLayout()
      })
    },

    handleCustomGroupConfirm(selectedKeys) {
      this.selectedGroups = selectedKeys
      saveGroupsByPageType(selectedKeys, this.config.localKeys.list, this.config.localKeys.detail, this.isChannelPage)
      this.fetchData()
      this.$nextTick(() => {
        this.$refs.tableRef.doLayout()
      })
    },

    handExport() {
      const groupFieldStr = getGroupKeysString(this.selectedGroups, this.tableColumns)
      let processedQuery = processMultipleSelect(this.listQuery, this.config.multipleSelectKeys)

      // 移除隐藏的搜索项
      processedQuery = removeHiddenSearchItems(processedQuery, this.config.hideSearchItems)

      const params = {
        ...processedQuery,
        [this.config.paramKeys.groupField]: groupFieldStr,
        token: this.$store.getters.authorization
      }

      window.location.href = this.exportRequest(params)
    },

    handleViewDetails(row) {
      const { date } = row
      let processedQuery = processMultipleSelect(this.listQuery, this.config.multipleSelectKeys)
      const { startDate, endDate, ...otherParams } = processedQuery

      // 移除隐藏的搜索项
      const filteredParams = removeHiddenSearchItems(otherParams, this.config.hideSearchItems)

      // 确保日期参数是字符串类型
      const params = {
        ...filteredParams,
        startDate: String(date || ''),
        endDate: String(date || '')
      }

      saveQueryParamsByPageType(params, this.config.localKeys.list, this.config.localKeys.detail, true)

      // 同时保存到marketDataMonitoring-panelDetail
      saveQueryParamsByPageType({
        startDate: String(date || ''),
        endDate: String(date || '')
      }, 'marketDataMonitoring-panelDetail', '', false)

      // console.log('******************* params: ', params)

      this.$router.push({
        path: this.config.channelPath,
        query: params
      })
    },

    tableRowClassName({ row }) {
      if (row.date === '综合汇总' || row.channelCode === '综合汇总') {
        return 'blue-row'
      }
    },

    handleSortChange({ column, prop, order }) {
      if (order) {
        const [first, ...rest] = this.tableData
        const list = [...rest].sort((a, b) => {
          let value1 = a[prop]
          let value2 = b[prop]
          try {
            value1 = extractNumber(value1)
            value2 = extractNumber(value2)
          } catch (error) {}

          return order === 'ascending' ? value1 - value2 : value2 - value1
        })
        this.tableDataBySort = [first, ...list]
      } else {
        this.tableDataBySort = this.tableData
      }
    }
  }
}
</script>

<style lang="scss" src="./index.scss" scoped></style>
