<template>
  <el-form ref="addForm" :model="addForm" :rules="addRules" label-position="top" class="demo-ruleForm1">
    <div class="form_view">
      <div class="form_view_title">
        <div class="title_line" />
        <span>基础信息</span>
      </div>
      <div class="flex">
        <el-form-item
          label="全流程媒体"
          prop="apiCode"
          :rules="addRules.common"
          style="display: inline-block; margin-right: 24px"
        >
          <el-select :disabled="!!addForm.id" v-model="addForm.apiCode" placeholder="请选择全流程媒体">
            <el-option
              v-for="item in mediaList"
              :key="item.platformCode"
              :label="item.platformName"
              :value="item.platformCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="规则名称" prop="ruleName" :rules="addRules.common" style="display: inline-block">
          <el-input
            style="width: 480px"
            v-model="addForm.ruleName"
            placeholder="请输入规则名称"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>
      </div>
      <el-form-item label="规则备注" prop="remark">
        <el-input
          v-model="addForm.remark"
          placeholder="请输入规则备注"
          maxlength="100"
          type="textarea"
          show-word-limit
        />
      </el-form-item>
    </div>

    <div class="form_view">
      <div class="form_view_title">
        <div class="title_line" />
        <span>撞库规则</span>
      </div>
      <div class="flex">
        <el-form-item
          label="城市"
          prop="limitCityStatus"
          :rules="addRules.city"
          style="display: inline-block; margin-right: 24px"
        >
          <el-radio-group v-model="addForm.limitCityStatus">
            <el-radio-button v-for="item in radioList" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          ref="limitCityList"
          :show-message="false"
          v-if="addForm.limitCityStatus === defaultZdyValue"
          label=""
          prop="limitCityList"
          :rules="addRules.city"
          :style="{display: 'inline-block'}"
        >
          <CitySelector
            ref="citySelector"
            v-model="addForm.limitCityList"
            multiple
            filterable
            placeholder="请选择城市"
          />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item
          label="年龄"
          prop="limit_age_type"
          :rules="addRules.age"
          style="display: inline-block; margin-right: 24px"
        >
          <el-radio-group v-model="addForm.limit_age_type">
            <el-radio-button v-for="item in radioList" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <div style="display: inline-block" v-if="addForm.limit_age_type === defaultZdyValue">
          <el-form-item
            label=""
            :show-message="false"
            prop="limitMinAge"
            :rules="addRules.age"
            :style="{display: 'inline-block'}"
          >
            <el-input
              style="width: 80px"
              v-model="addForm.limitMinAge"
              oninput="value=value.replace(/[^0-9]/g,'')"
              @blur="addForm.limitMinAge = $event.target.value"
            ></el-input>
          </el-form-item>
          <span>-</span>
          <el-form-item
            label=""
            :show-message="false"
            prop="limitMaxAge"
            :rules="addRules.age"
            :style="{display: 'inline-block'}"
          >
            <el-input
              style="width: 80px"
              v-model="addForm.limitMaxAge"
              oninput="value=value.replace(/[^0-9]/g,'')"
              @blur="addForm.limitMaxAge = $event.target.value"
            ></el-input>
          </el-form-item>
          <span>（注意：年龄区间为闭区间，左侧为小值，右侧为大值）</span>
        </div>
      </div>
      <div class="flex">
        <el-form-item
          label="车辆状态"
          prop="car_type"
          :rules="addRules.vehicle"
          style="display: inline-block; margin-right: 24px"
        >
          <el-radio-group v-model="addForm.car_type">
            <el-radio-button v-for="item in radioList" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          ref="car"
          v-if="addForm.car_type === defaultZdyValue"
          label=""
          :show-message="false"
          prop="car"
          :rules="addRules.vehicle"
          :style="{display: 'inline-block'}"
        >
          <el-select clearable multiple v-model="addForm.car" placeholder="请选择">
            <el-option v-for="item in vehicleList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item
          label="姓名"
          prop="limitName_type"
          :rules="addRules.name"
          style="display: inline-block; margin-right: 24px"
        >
          <el-radio-group v-model="addForm.limitName_type">
            <el-radio-button v-for="item in radioList" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          ref="limitName"
          v-if="addForm.limitName_type === defaultZdyValue"
          label=""
          :show-message="false"
          prop="limitName"
          :rules="addRules.name"
          :style="{display: 'inline-block'}"
        >
          <el-select v-model="addForm.limitName" placeholder="请选择姓名限制">
            <el-option v-for="item in nameList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </div>
    </div>

    <div class="form_view">
      <div class="form_view_title">
        <div class="title_line" />
        <span>结算规则</span>
      </div>
      <div>
        <el-form-item
          label="规则"
          prop="settlementMethod"
          :rules="addRules.common"
          style="display: inline-block; margin-right: 24px"
        >
          <el-radio-group v-model="addForm.settlementMethod" @change="$event === '0' && (addForm.mediaCommissionSharing = '')">
            <el-radio-button v-for="(item, index) in Object.keys(SETTLEMENT_METHOD_MAP)" :key="index" :label="item">
              {{ SETTLEMENT_METHOD_MAP[item] }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
      </div>
      <el-form-item
        v-if="String(addForm.settlementMethod) === '1'"
        label="媒体佣金分润"
        prop="mediaCommissionSharing"
        :rules="addRules.method"
        style="display: inline-block"
      >
        <el-input
          style="width: 120px; display: inline-block"
          v-model="addForm.mediaCommissionSharing"
          placeholder="百分点"
          oninput="value=value.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1')"
          @blur="addForm.mediaCommissionSharing = $event.target.value"
        />
        <span>%</span>
      </el-form-item>
    </div>
    <div class="form_view">
      <div class="form_view_title">
        <div class="title_line" />
        <span>线索提交规则</span>
      </div>
      <el-form-item
        label="线索提交是否校验资方存证状态"
        prop="limitCheckCertification"
        :rules="addRules.common"
        style="display: inline-block; margin-right: 24px"
      >
        <el-radio-group v-model="addForm.limitCheckCertification">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="延迟提交线索时间"
        prop="delzySubmitMinues"
        :rules="addRules.common"
        style="display: inline-block"
      >
        <el-input
          style="width: 120px; display: inline-block"
          v-model="addForm.delzySubmitMinues"
          placeholder="请输入时间"
          oninput="value=value.replace(/[^0-9]/g,'')"
          @blur="addForm.delzySubmitMinues = $event.target.value"
        />
        <span>(min)</span>
      </el-form-item>
    </div>
    <div class="form_view">
      <div class="form_view_title">
        <div class="title_line" />
        <span> 状态</span>
      </div>
      <el-form-item label="状态" prop="onlineStatus" :rules="addRules.common">
        <el-radio-group v-model="addForm.onlineStatus">
          <el-radio :label="1">上线</el-radio>
          <el-radio :label="0">下线</el-radio>
        </el-radio-group>
      </el-form-item>
    </div>
    <div :style="{'text-align': 'right', width: '100%'}">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button :loading="subLoading" type="primary" @click="handMessageStyleListAdd">确认</el-button>
    </div>
  </el-form>
</template>
<script>
import CitySelector from '@/components/CitySelector/index.vue'
import utils from '@/libs/utils'
import {SETTLEMENT_METHOD_MAP} from '@/views/newChannel/enum/mediaList'
const {isEmpty} = utils
const defaultForm = {
  apiCode: '',
  ruleName: '',
  remark: '',
  limitCityStatus: -1,
  limitCityList: [],
  limit_age_type: -1,
  limitMinAge: '',
  limitMaxAge: '',
  car_type: -1,
  car: [],
  limitName_type: -1,
  limitName: '',
  settlementMethod: 0,
  mediaCommissionSharing: '',
  limitCheckCertification: '',
  delzySubmitMinues: '',
  onlineStatus: 1
}
const defaultZdyValue = 1
export default {
  props: {
    modelForm: {
      type: Object,
      default: () => ({})
    },
    mediaList: {
      type: Array,
      default: () => []
    },
    subLoading: {
      type: Boolean,
      default: false
    }
  },
  components: {
    CitySelector
  },
  data() {
    const cityValidator = (rule, value, callback) => {
      return relevanceValidator(rule, value, callback, 'limitCityStatus', 'limitCityList', '请选择城市')
    }
    const ageValidator = (rule, value, callback) => {
      if (this.addForm.limit_age_type === defaultZdyValue) {
        if (!isEmpty(this.addForm.limitMinAge) && !isEmpty(this.addForm.limitMaxAge)) {
          if (this.addForm.limitMinAge < 0 || this.addForm.limitMaxAge < 0) {
            return callback(new Error('年龄不能小于0'))
          }
          if (Number(this.addForm.limitMinAge) > Number(this.addForm.limitMaxAge)) {
            if (['limitMinAge', 'limitMaxAge'].includes(rule.field)) {
              this.$refs.addForm.validateField('limit_age_type')
            }
            return callback(new Error('年龄范围有误'))
          }
        } else {
          return callback(new Error('请输入年龄范围'))
        }
      }
      this.$refs.addForm.clearValidate(['limit_age_type', 'limitMinAge', 'limitMaxAge'])
      callback()
    }
    const vehicleValidator = (rule, value, callback) => {
      return relevanceValidator(rule, value, callback, 'car_type', 'car', '请选择车辆状态')
    }
    const nameValidator = (rule, value, callback) => {
      return relevanceValidator(rule, value, callback, 'limitName_type', 'limitName', '请选择姓名限制')
    }
    const relevanceValidator = (rule, value, callback, preKey, validKey, errorMessage) => {
      if (
        this.$refs[validKey] &&
        (isEmpty(this.addForm[validKey]) || (Array.isArray(this.addForm[validKey]) && !this.addForm[validKey].length))
      ) {
        if (rule.field === validKey) {
          this.$refs.addForm.validateField(preKey)
        }
        return callback(new Error(errorMessage))
      }
      this.$refs.addForm.clearValidate([preKey, validKey])
      callback()
    }
    const methodValidator = (rule, value, callback) => {
      // 数字范围大于0，小于等于50，允许保留两位小数
      const reg = /^[0-9]+(.[0-9]{1,2})?$/
      if (!reg.test(value)) {
        return callback(new Error('允许保留两位小数'))
      }
      if (value <= 0 || value > 50) {
        return callback(new Error('数字范围大于0，小于等于50'))
      }
      callback()
    }
    return {
      defaultZdyValue,
      addForm: {},
      radioList: [
        {label: '不限', value: -1},
        {label: '自定义', value: defaultZdyValue}
      ],
      vehicleList: [
        {label: '无车', value: 0},
        {label: '有车可抵', value: 20},
        {label: '有车不可抵', value: 10}
      ],
      nameList: [
        {label: '姓名有值', value: 2}
        // {label: '姓名无值', value: 3}
      ],
      addRules: {
        common: [{required: true, message: '必填项', trigger: 'blur'}],
        city: [
          {validator: cityValidator, trigger: 'change'},
          {required: true, message: '必填项', trigger: 'change'}
        ],
        age: [
          {validator: ageValidator, trigger: 'change'},
          {required: true, message: '必填项', trigger: 'change'}
        ],
        vehicle: [
          {validator: vehicleValidator, trigger: 'change'},
          {required: true, message: '必填项', trigger: 'change'}
        ],
        name: [
          {validator: nameValidator, trigger: 'change'},
          {required: true, message: '必填项', trigger: 'change'}
        ],
        method: [
          {required: true, message: '必填项', trigger: 'change'},
          {validator: methodValidator, trigger: 'change'},
        ]
      },
      checkedCityNodes: [],
      SETTLEMENT_METHOD_MAP
    }
  },
  mounted() {
    const {limitMinAge, limitMaxAge, car, limitName, ...rest} = this.modelForm || {}
    this.addForm = {...defaultForm, ...rest}
    this.checkedCityNodes =
      this.modelForm?.limitCityList?.map(item => ({...item, label: item.cityName, value: item.cityCode})) ?? []
    this.addForm.limitCityList = this.modelForm?.limitCityList?.map(item => item.cityCode) ?? []
    const nameList = this.nameList.map(item => item.value)
    const carList = this.vehicleList.map(item => item.value)
    limitMaxAge >= 0 &&
      limitMinAge >= 0 &&
      Object.assign(this.addForm, {limit_age_type: defaultZdyValue, limitMinAge, limitMaxAge})
    nameList.includes(limitName) && Object.assign(this.addForm, {limitName_type: defaultZdyValue, limitName})
    const carArray = (String(car)?.split(',') ?? []).map(el => Number(el))
    let index = carArray.findIndex(item => carList.includes(item))
    index !== -1 && Object.assign(this.addForm, {car_type: defaultZdyValue, car: [...carArray]})
  },
  methods: {
    handMessageStyleListAdd() {
      this.$refs.addForm.validate(valid => {
        if (valid) {
          let limitCityList = []
          this.addForm.limitCityStatus === defaultZdyValue &&
            (limitCityList =
              this.$refs?.citySelector
                ?.getCheckedNodes?.()
                ?.map(item => ({cityName: item.label, cityCode: item.value})) ?? [])

          let {limitMinAge, limitMaxAge, car, limitName, limit_age_type, car_type, limitName_type} = this.addForm
          limit_age_type === -1 ? ((limitMinAge = -1), (limitMaxAge = -1)) : null
          car_type === -1 ? (car = '-1') : (car = car.join(','))
          limitName_type === -1 ? (limitName = 1) : null
          const params = {...this.addForm, limitMinAge, limitMaxAge, car, limitName, limitCityList}
          delete params.limit_age_type
          delete params.car_type
          delete params.limitName_type
          this.$emit('confirm', params)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.form_view {
  background-color: #ffffff;
  width: 100%;
  padding: 15px 15px 10px;
  border-radius: 5px;
  margin-bottom: 0;
  .form_view_title {
    margin-bottom: 15px;
    .title_line {
      width: 2px;
      height: 10px;
      background-color: #66b1ff;
      display: inline-block;
      vertical-align: middle;
    }

    span {
      padding-left: 5px;
      vertical-align: middle;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 14px;
      color: #333333;
      line-height: 30px;
      text-align: left;
      font-style: normal;
    }
  }
}
.demo-ruleForm1 {
  ::v-deep .el-form--label-top .el-form-item__label {
    padding-bottom: 0;
  }
}
</style>
